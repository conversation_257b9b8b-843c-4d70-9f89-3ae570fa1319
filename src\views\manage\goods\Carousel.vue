<!--  -->
<template>
  <div>
    <div>
      <el-table :data="tableData"  stripe style="width: 80%;margin: 2px auto">
        <el-table-column label="商品">
          <template  v-slot:default="scope">
            <a :href="'/goodView/'+scope.row.goodId" width="60">{{scope.row.goodName}}</a>
          </template>
        </el-table-column>
        <el-table-column  label="图片" >
          <template    v-slot:default="scope">
            <img :src="baseApi + scope.row.img" width="300" height="185" />
          </template>
        </el-table-column>
        <el-table-column prop="showOrder" label="轮播顺序"></el-table-column>

        <el-table-column fixed="right" label="操作" width="180">
          <template  v-slot:default="scope">
            <el-button
              type="primary"
              style="font-size: 18px"
              @click="edit(scope.row)"
            >编辑</el-button>
             <el-button
                  type="danger"
                  style="margin-left: 10px; font-size: 18px"
                  @click="handleDelete(scope.row.id)"
                >
                  删除
                </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
<!--新增按钮-->
    <div style="text-align: center">
      <el-button @click="add" type="primary" style="margin: 30px;width: 150px; font-size: 20px;">
        
        新增
      </el-button>
    </div>
    <!-- 弹窗   -->

    <el-dialog title="信息" v-model="dialogFormVisible" width="30%"
               :close-on-click-modal="false">
      <el-form :model="entity">
        <el-form-item label="商品id" label-width="150px">
          <el-input v-model="entity.goodId"  style="width: 80%"></el-input>
        </el-form-item>
        <el-form-item label="轮播顺序" label-width="150px">
<!--          <el-input v-model="entity.showOrder" autocomplete="off" style="width: 80%"></el-input>-->
          <el-select v-model="entity.showOrder">
            <el-option v-for="index in tableData.length" :key="index" :label="index" :value="index">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div  class="dialog-footer">
        <el-button @click="dialogFormVisible = false" style="font-size: 20px;"> 取消</el-button>
        <el-button type="primary" @click="save" style="font-size: 20px;"> 确定</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { apiRequest } from "@/utils/request";
import { baseURL } from "@/utils/request";
import { ElMessage, ElMessageBox } from "element-plus";
export default {
  name: 'Carousel', // ✅ 推荐使用多词组件名

  // 引入的组件需要注册
  components: {},

  // 数据区
  data() {
    return {
      baseApi: baseURL,
      tableData: [],
      dialogFormVisible:false,
      options: [],
      searchText: '',
      entity: {},
    };
  },

  // 计算属性
  computed: {},

  // 监听数据变化
  watch: {},

  // 方法集合
  methods: {
    
    async load() {
      try {
        const res = await apiRequest({
          url: "/api/carousel/",
          method: "get",
          params: {
          },
        });
        if (res.code === "200") {
          this.tableData = res.data;
          console.log("res.data:"+res.data)
        } 
      } catch (e) {
         ElMessage({
            showClose: true,
            message: e.message || "请求失败",
            type: "error",
            duration: 5000,
          });
      }
    },
    add() {
      this.entity = {}
      this.tableData.length++;
      this.dialogFormVisible = true
    },
    edit(row) {
      this.entity = JSON.parse(JSON.stringify(row))
      this.dialogFormVisible = true
    },
    //保存轮播图数据集  是否修改 根据 
   async save(){
      if(this.entity.goodId == undefined || this.entity.goodId === "") {
          this.$message.error("商品id不能为空")
          return;
      }
      if(this.entity.showOrder == undefined) {
          this.$message.error("轮播顺序不能为空")
          return;
      }
      var saveOrUpdateurl="/api/carousel/save"
      try {
        const res = await apiRequest({
          url: saveOrUpdateurl,
          method: "post",
          data: this.entity,
        });
        if (res.code === "200") {
          this.dialogFormVisible = false;
           this.load();
        } else {
          ElMessage.error(res.msg);
        }
      } catch (e) {
        console.error(e);
        ElMessage({
          showClose: true,
          message: e.message || e,
          type: "error",
          duration: 5000,
        });
      }
    },
    // 单个删除
    async handleDelete(id) {
      try {
        await ElMessageBox.confirm("确认删除该文件吗？", "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        });
        const res = await apiRequest({
          url: `/api/carousel/${id}`,
          method: "delete",
        });
        if (res.code === "200") {
          ElMessage.success("删除成功");
          this.load();
        } else {
          ElMessage.error(res.msg || "删除失败");
        }
      } catch (e) {
        if (e !== "cancel" && !this.isUnmounted) {
          ElMessage.error("删除失败：");
        }
      }
    },

  },

  // 生命周期 - 创建完成
  created() {
    // 
    this.load();



  },

  // 生命周期 - 挂载完成
  mounted() {
    // 
  },

  // 生命周期 - 更新之前
  beforeUpdate() {},

  // 生命周期 - 更新之后
  updated() {},

  // 生命周期 - 卸载前
  beforeUnmount() {
    // 替代 beforeDestroy
    // 
  },

  // 生命周期 - 卸载后
  unmounted() {
    // 替代 destroyed
    // 
  },

  // keep-alive 缓存组件激活时触发
  activated() {},

  // keep-alive 缓存组件失活时触发
  deactivated() {}
}
</script>

<style scoped>

</style>