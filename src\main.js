// src/main.js
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import router from './router'
 import store from './store';
 import request from '@/utils/request'
// 创建应用
const app = createApp(App)
// initialize(app);
// 全局混入（Vue 3）
app.mixin({
  created() { /* ... */ },
});
app.config.globalProperties.$request = request
// ✅ 使用 Pinia（注意：有括号！）
app.use(createPinia())

// 使用 Element Plus
app.use(ElementPlus)

// 使用 Vue Router
app.use(router)

 app.use(store);  
// // 全局原型（替代 Vue.prototype）
import axios from 'axios';
 app.config.globalProperties.$http = axios;
// 挂载
app.mount('#app')