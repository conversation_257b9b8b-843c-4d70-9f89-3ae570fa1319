<template>
  <div>
    <div class="demo-input-size">
      <el-select
        v-model="searchMode"
        placeholder="请选择"
        style="width: 150px; margin-right: 10px"
      >
        <el-option value="id" label="用户id"></el-option>
        <el-option value="username" label="账号"></el-option>
        <el-option value="nickname" label="昵称"></el-option>
      </el-select>
      <el-input
        v-if="searchMode === 'id'"
        placeholder="请输入用户id"
        prefix-icon="el-icon-search"
        style="width: 250px; padding-right: 5px"
        v-model="searchParams.id"
      ></el-input>
      <el-input
        v-if="searchMode === 'username'"
        placeholder="请输入账号"
        prefix-icon="el-icon-search"
        style="width: 250px; padding-right: 5px"
        v-model="searchParams.username"
      ></el-input>
      <el-input
        v-if="searchMode === 'nickname'"
        placeholder="请输入昵称"
        prefix-icon="el-icon-search"
        style="width: 250px; padding-right: 5px"
        v-model="searchParams.nickname"
      ></el-input>
      <el-button type="primary" @click="search"> 搜索 </el-button>
      <el-button type="warning" @click="reload"> 重置 </el-button>
    </div>
    <!--          按钮栏-->
    <div style="padding-top: 10px">
      <el-button type="primary" @click="handleAdd" style="font-size: 18px">
        新增</el-button
      >
      <el-button type="danger" @click="delBatch" style="font-size: 18px">
        批量删除</el-button
      >
    </div>
    <!--          弹窗-->
    <el-dialog :title="dialogTitle" v-model="dialogFormVisible">
      <el-form label-width="50px" style="padding: 0 60px">
        <el-form-item label="账号" v-if="dialogTitle == '新增用户'">
          <el-input v-model="user.username" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="密码" v-if="dialogTitle == '新增用户'">
          123456
        </el-form-item>
        <el-form-item label="昵称">
          <el-input v-model="user.nickname" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="身份">
          <el-select v-model="user.role" placeholder="请选择">
            <el-option
              v-for="item in roleOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="电话">
          <el-input v-model="user.phone" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="邮箱">
          <el-input v-model="user.email" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="地址">
          <el-input v-model="user.address" autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogFormVisible = false" style="font-size: 20px"
            >取消</el-button
          >
          <el-button type="primary" @click="save" style="font-size: 20px"
            >确定</el-button
          >
        </div>
      </template>
    </el-dialog>

    <!--          表格-->
    <el-table
      :data="tableData"
      background-color="black"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection"></el-table-column>
      <el-table-column prop="id" label="id" width="100"></el-table-column>
      <el-table-column
        prop="username"
        label="账号"
        width="150"
      ></el-table-column>
      <el-table-column label="身份" width="150">
        <template v-slot:default="scope">
          <span v-if="scope.row.role === 'user'">用户</span>
          <span v-if="scope.row.role === 'admin'">管理员</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="nickname"
        label="昵称"
        width="180"
      ></el-table-column>
      <el-table-column prop="phone" label="电话" width="180"></el-table-column>
      <el-table-column prop="email" label="邮箱" width="180"></el-table-column>
      <el-table-column
        prop="address"
        label="地址"
        width="350"
      ></el-table-column>
      <el-table-column label="操作" width="250" fixed="right">
        <template v-slot:default="scope">
          <el-button
            style="font-size: 18px"
            type="success"
            @click="handleEdit(scope.row)"
          >
            编辑
          </el-button>
          <el-button
            style="font-size: 18px"
            type="danger"
            @click="handleDelete(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="block" style="flex: 0 0 auto">
      <el-pagination
        :current-page="currentPage"
        :page-sizes="[3, 5, 8, 10]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentPage"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import md5 from "js-md5";
import { apiRequest } from "@/utils/request";
import { ElMessage, ElMessageBox } from 'element-plus'
export default {
  name: "User",

  created() {
    //加载用户信息
    this.load();
  },
  data() {
    return {
      tableData: [],
      roleOptions: [
        {
          value: "admin",
          label: "管理员",
        },
        {
          value: "user",
          label: "用户",
        },
      ],
      roleValue: "",
      total: 0,
      pageSize: 5,
      currentPage: 1,
      searchMode: "id",
      searchParams: {
        id: "",
        username: "",
        nickname: "",
      },
      dialogFormVisible: false,
      dialogTitle: "",
      user: {
        username: "",
        nickname: "",
        newPassword: "",
        role: "",
        phone: "",
        email: "",
        address: "",
      },
      multipleSelection: [],
    };
  },
  methods: {
    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.load();
    },
    handleCurrentPage(currentPage) {
      this.currentPage = currentPage;
      this.load();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    //加载用户信息
    async load() {
      try {
        const res = await apiRequest({
          url: "/userAPI/user/page",
          method: "get",
          params: {
            pageNum: this.currentPage,
            pageSize: this.pageSize,
            id: this.searchParams.id,
            username: this.searchParams.username,
            nickname: this.searchParams.nickname,
          },
        });
        if (res.code === "200") {
          this.tableData = res.data.records;
          this.total = res.data.total;
        } else {
          ElMessage.error(res.msg || "获取用户信息失败");
        }
      } catch (e) {
        console.error(e);
        ElMessage({
          showClose: true,
          message: e.message || e,
          type: "error",
          duration: 5000,
        });
      }
    },
    search() {
      this.currentPage = 1;
      this.load();
    },
    reload() {
      this.searchParams.id = "";
      this.searchParams.username = "";
      this.searchParams.nickname = "";
      this.load();
    },

    //插入或修改
    async save() {
      if (this.dialogTitle == "新增用户") {
        if (this.user.username.trim() == "") {
          this.$message.error("账号不能为空");
          return;
        }
        this.user.newPassword = md5("123456");
      }
      if (this.user.nickname.trim() == "") {
        this.$message.error("昵称不能为空");
        return;
      }
      if (this.user.role.trim() == "") {
        this.$message.error("身份不能为空");
        return;
      }
      if (this.user.phone.trim() == "") {
        this.$message.error("电话不能为空");
        return;
      }
      if (this.user.email.trim() == "") {
        this.$message.error("邮箱不能为空");
        return;
      }
      this.dialogTitle = "新增用户";
      const submitData = {
        id: this.user.id,
        username: this.user.username,
        nickname: this.user.nickname,
        newPassword: this.user.newPassword,
        role: this.user.role,
        phone: this.user.phone,
        email: this.user.email,
        address: this.user.address,
      };

      try {
        const res = await apiRequest({
          url: "/userAPI/saveUpdate",
          method: "post",
          data: submitData,
        });
        if (res.code === "200") {
          (this.dialogFormVisible = false), this.load();
        } else {
          ElMessage.error(res.msg);
        }
      } catch (e) {
        console.error(e);
        ElMessage({
          showClose: true,
          message: e.message || e,
          type: "error",
          duration: 5000,
        });
      }
    },
    handleAdd() {
      this.dialogTitle = "新增用户";
      this.dialogFormVisible = true;
      this.user = {
        username: "",
        nickname: "",
        newPassword: "",
        role: "",
        phone: "",
        email: "",
        address: "",
      };
    },
    //编辑
    handleEdit(row) {
      this.user = JSON.parse(JSON.stringify(row));
      this.dialogTitle = "编辑用户";
      this.dialogFormVisible = true;
    },
    //删除
    async handleDelete(id) {
      try {
        await this.$confirm("确认删除该用户吗?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        });
        const res = await apiRequest({
          url: "/userAPI/user/" + id,
          method: "delete",
        });
        console.log("res：", res); // 使用对象打印，不要用字符串拼接
        if (res.code === "200") {
          ElMessage.success("删除成功");
          this.load(); // 刷新列表
        } else {
          ElMessage.error(res.msg || "操作失败");
        }
      } catch (e) {
        // 用户取消删除 或 请求失败
        if (e !== "cancel") {
          console.error("请求出错：", e);
          ElMessage({
            type: "error",
            message: "网络异常，请重试",
            duration: 5000,
          });
        }
        // 如果是取消，不提示错误
      }
    },
    // 批量删除
    async delBatch() {
      // 检查是否有选中项
      if (!this.multipleSelection.length) {
        ElMessage.warning("请先选择要删除的用户");
        return;
      }
      const ids = this.multipleSelection.map((v) => v.id);
      console.log("选中的用户ID：", ids);
      try {
        // 弹出确认框
        await ElMessageBox.confirm("确认删除这些用户吗？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        });
        // 发起批量删除请求
        const res = await apiRequest({
          url: "/userAPI/user/del/batch",
          method: "post",
          data: ids, // 假设后端接收一个 ID 数组
        });
        console.log("后端响应：", res);
        // 判断业务状态码
        if (res.code === "200") {
          ElMessage.success("批量删除成功");
          this.load(); // 刷新列表
        } else {
          ElMessage.error(res.msg || "批量删除失败");
        }
      } catch (e) {
        // 用户取消或请求失败
        if (e !== "cancel") {
          console.error("批量删除请求异常：", e);
          ElMessage({
            type: "error",
            message: "网络异常，请重试",
            duration: 5000,
          });
        } else {
          ElMessage.info("已取消删除");
        }
      }
    },
  },
};
</script>

<style scoped>
</style>