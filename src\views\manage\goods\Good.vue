<template>
  <div class="goods-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>首页</el-breadcrumb-item>
        <el-breadcrumb-item>商品/商品管理</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 搜索和操作区域 -->
    <div class="operation-bar">
      <div class="search-area">
        <el-button type="primary" @click="handleSearch" style="font-size: 16px;">
          <el-icon><Search /></el-icon>
          搜索
        </el-button>
        <el-button type="warning" @click="handleBatch" style="font-size: 16px;">
          <el-icon><Orange /></el-icon>
          批量
        </el-button>
        <el-button type="success" @click="handleAdd" style="font-size: 16px;">
          <el-icon><Plus /></el-icon>
          新增
        </el-button>
      </div>
    </div>

    <!-- 商品表格 -->
    <div class="table-container">
      <el-table 
        :data="tableData" 
        stripe 
        style="width: 100%"
        :header-cell-style="{ backgroundColor: '#f0f9ff', color: '#333' }"
      >
        <el-table-column prop="id" label="商品id" width="80" align="center"></el-table-column>
        <el-table-column label="商品名称" width="150" align="center">
          <template #default="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="商品分类" width="120" align="center">
          <template #default="scope">
            <span>{{ scope.row.categoryName || '未分类' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="商品图片" width="100" align="center">
          <template #default="scope">
            <el-image
              v-if="scope.row.imgs"
              :src="baseApi + scope.row.imgs"
              style="width: 60px; height: 60px; border-radius: 4px;"
              fit="cover"
              :preview-src-list="[baseApi + scope.row.imgs]"
            />
            <span v-else>无图片</span>
          </template>
        </el-table-column>
        <el-table-column prop="price" label="价格" width="100" align="center">
          <template #default="scope">
            <span style="color: #f56c6c; font-weight: bold;">¥{{ scope.row.price }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="num" label="库存" width="80" align="center"></el-table-column>
        <el-table-column label="销售价格(元)" width="120" align="center">
          <template #default="scope">
            <span>{{ scope.row.salePrice || scope.row.price }}</span>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" width="120" align="center">
          <template #default="scope">
            <span>{{ formatDate(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="80" align="center">
          <template #default="scope">
            <el-switch
              v-model="scope.row.status"
              :active-value="1"
              :inactive-value="0"
              @change="handleStatusChange(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" align="center" fixed="right">
          <template #default="scope">
            <el-button 
              type="primary" 
              size="small" 
              @click="handleEdit(scope.row)"
              style="margin-right: 5px;"
            >
              <el-icon><Edit /></el-icon>
              修改
            </el-button>
            <el-button 
              type="danger" 
              size="small" 
              @click="handleDelete(scope.row)"
            >
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <div class="pagination-info">
        <span>共 {{ total }} 条</span>
        <span style="margin-left: 20px;">{{ currentPage }}/{{ Math.ceil(total/pageSize) }}</span>
      </div>
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[5, 10, 20, 50]"
        :total="total"
        layout="prev, pager, next"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog 
      v-model="dialogVisible" 
      :title="dialogTitle" 
      width="600px"
      :before-close="handleClose"
    >
      <el-form 
        :model="form" 
        :rules="rules" 
        ref="formRef" 
        label-width="120px"
      >
        <el-form-item label="商品名称" prop="name">
          <el-input 
            v-model="form.name" 
            placeholder="请输入商品名称"
            maxlength="100"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item label="商品分类" prop="categoryId">
          <el-select 
            v-model="form.categoryId" 
            placeholder="请选择商品分类"
            style="width: 100%"
          >
            <el-option
              v-for="category in categories"
              :key="category.id"
              :label="category.name"
              :value="category.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="商品价格" prop="price">
          <el-input-number
            v-model="form.price"
            :min="0"
            :precision="2"
            placeholder="请输入商品价格"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="库存数量" prop="num">
          <el-input-number
            v-model="form.num"
            :min="0"
            placeholder="请输入库存数量"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="商品描述" prop="description">
          <el-input 
            v-model="form.description" 
            type="textarea" 
            :rows="4"
            placeholder="请输入商品描述"
            maxlength="500"
            show-word-limit
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Orange, Plus, Edit, Delete } from '@element-plus/icons-vue'
import { apiRequest, baseURL } from '@/utils/request'

export default {
  name: 'GoodsManage',
  components: {
    Search,
    Orange,
    Plus,
    Edit,
    Delete
  },
  setup() {
    // 响应式数据
    const tableData = ref([])
    const categories = ref([])
    const total = ref(0)
    const currentPage = ref(1)
    const pageSize = ref(10)
    const dialogVisible = ref(false)
    const dialogTitle = ref('')
    const formRef = ref()
    const baseApi = baseURL

    // 表单数据
    const form = reactive({
      id: null,
      name: '',
      categoryId: null,
      price: 0,
      num: 0,
      description: '',
      imgs: '',
      status: 1
    })

    // 表单验证规则
    const rules = {
      name: [
        { required: true, message: '请输入商品名称', trigger: 'blur' },
        { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
      ],
      categoryId: [
        { required: true, message: '请选择商品分类', trigger: 'change' }
      ],
      price: [
        { required: true, message: '请输入商品价格', trigger: 'blur' },
        { type: 'number', min: 0, message: '价格不能小于0', trigger: 'blur' }
      ],
      num: [
        { required: true, message: '请输入库存数量', trigger: 'blur' },
        { type: 'number', min: 0, message: '库存不能小于0', trigger: 'blur' }
      ]
    }

    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN')
    }

    // 加载商品数据
    const loadGoods = async () => {
      try {
        const res = await apiRequest({
          url: '/api/good/page',
          method: 'get',
          params: {
            pageNum: currentPage.value,
            pageSize: pageSize.value
          }
        })
        if (res.code === '200') {
          tableData.value = res.data.records || []
          total.value = res.data.total || 0
        } else {
          ElMessage.error(res.msg || '获取商品数据失败')
        }
      } catch (error) {
        console.error('加载商品数据失败:', error)
        ElMessage.error('加载商品数据失败')
      }
    }

    // 加载分类数据
    const loadCategories = async () => {
      try {
        const res = await apiRequest({
          url: '/api/category/list',
          method: 'get'
        })
        if (res.code === '200') {
          categories.value = res.data || []
        }
      } catch (error) {
        console.error('加载分类数据失败:', error)
      }
    }

    // 搜索
    const handleSearch = () => {
      currentPage.value = 1
      loadGoods()
    }

    // 批量操作
    const handleBatch = () => {
      ElMessage.info('批量操作功能开发中...')
    }

    // 新增商品
    const handleAdd = () => {
      dialogTitle.value = '新增商品'
      Object.assign(form, {
        id: null,
        name: '',
        categoryId: null,
        price: 0,
        num: 0,
        description: '',
        imgs: '',
        status: 1
      })
      dialogVisible.value = true
    }

    // 编辑商品
    const handleEdit = (row) => {
      dialogTitle.value = '编辑商品'
      Object.assign(form, { ...row })
      dialogVisible.value = true
    }

    // 删除商品
    const handleDelete = async (row) => {
      try {
        await ElMessageBox.confirm(
          `确定要删除商品"${row.name}"吗？`,
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        
        const res = await apiRequest({
          url: `/api/good/${row.id}`,
          method: 'delete'
        })
        
        if (res.code === '200') {
          ElMessage.success('删除成功')
          loadGoods()
        } else {
          ElMessage.error(res.msg || '删除失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除商品失败:', error)
          ElMessage.error('删除失败')
        }
      }
    }

    // 状态变更
    const handleStatusChange = async (row) => {
      try {
        const res = await apiRequest({
          url: '/api/good/status',
          method: 'post',
          data: {
            id: row.id,
            status: row.status
          }
        })
        
        if (res.code === '200') {
          ElMessage.success('状态更新成功')
        } else {
          ElMessage.error(res.msg || '状态更新失败')
          // 恢复原状态
          row.status = row.status === 1 ? 0 : 1
        }
      } catch (error) {
        console.error('状态更新失败:', error)
        ElMessage.error('状态更新失败')
        // 恢复原状态
        row.status = row.status === 1 ? 0 : 1
      }
    }

    // 提交表单
    const handleSubmit = async () => {
      try {
        await formRef.value.validate()
        
        const url = form.id ? '/api/good/update' : '/api/good/save'
        const res = await apiRequest({
          url,
          method: 'post',
          data: form
        })
        
        if (res.code === '200') {
          ElMessage.success(form.id ? '修改成功' : '新增成功')
          dialogVisible.value = false
          loadGoods()
        } else {
          ElMessage.error(res.msg || '操作失败')
        }
      } catch (error) {
        console.error('提交失败:', error)
        ElMessage.error('操作失败')
      }
    }

    // 关闭对话框
    const handleClose = () => {
      dialogVisible.value = false
      formRef.value?.resetFields()
    }

    // 分页相关
    const handleSizeChange = (val) => {
      pageSize.value = val
      currentPage.value = 1
      loadGoods()
    }

    const handleCurrentChange = (val) => {
      currentPage.value = val
      loadGoods()
    }

    // 组件挂载时加载数据
    onMounted(() => {
      loadGoods()
      loadCategories()
    })

    return {
      tableData,
      categories,
      total,
      currentPage,
      pageSize,
      dialogVisible,
      dialogTitle,
      form,
      rules,
      formRef,
      baseApi,
      formatDate,
      loadGoods,
      handleSearch,
      handleBatch,
      handleAdd,
      handleEdit,
      handleDelete,
      handleStatusChange,
      handleSubmit,
      handleClose,
      handleSizeChange,
      handleCurrentChange
    }
  }
}
</script>

<style scoped>
.goods-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
  padding: 15px 20px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.operation-bar {
  margin-bottom: 20px;
  padding: 15px 20px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.search-area {
  display: flex;
  gap: 10px;
}

.table-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  overflow: hidden;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.pagination-info {
  color: #666;
  font-size: 14px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
