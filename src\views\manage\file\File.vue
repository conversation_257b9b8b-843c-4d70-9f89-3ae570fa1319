<!-- File.vue -->
<template>
   <div>
     <!-- 搜索栏 -->
    <div class="demo-input-size">
      <el-input placeholder="请输入文件名" v-model="fileName" class="inputSty"></el-input>
      <el-button type="primary" @click="search">搜索</el-button>
      <el-button type="warning" @click="reload">重置</el-button>
    </div>

    <!--          按钮栏-->
    <div style="padding-top: 10px">

      <el-upload action="http://localhost:9197/file/upload" :show-file-list="false" :on-success="handleFileUploadSuccess" style="display: inline-block">
        <el-button type="primary" style="font-size: 18px;"> 上传</el-button>
      </el-upload>
      <el-button type="danger" @click="delBatch" style="margin-left: 10px;font-size: 18px;"> 批量删除</el-button>
    </div>
    <!--          表格-->
    <el-table :data="tableData" background-color="black" @selection-change="handleSelectionChange" >
      <el-table-column type="selection" ></el-table-column>
      <el-table-column prop="name" label="文件名" width="350" ></el-table-column>
      <el-table-column prop="type" label="文件类型" width="180" ></el-table-column>
      <el-table-column prop="size" label="文件大小" width="180" ></el-table-column>
      <el-table-column label="操作" width="240" fixed="right">
        <template v-slot:default="scope">
        <!--下载-->
          <a :href="baseApi + scope.row.url">
            <el-button
              type="success"
              style="font-size: 18px;"
              >
               下载
            </el-button>
          </a>
         <!-- 删除-->
          <el-button
              type="danger"
              style="margin-left: 10px;font-size: 18px;"
              @click="handleDelete(scope.row.id)">
              删除
            </el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="block" style="flex: 0 0 auto">
      <el-pagination
          :current-page="currentPage"
          :page-sizes="[3, 5, 8, 10]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentPage"
      >
      </el-pagination>
    </div> 
   </div>
</template>

<script>
import { apiRequest } from "@/utils/request";
import { baseURL } from "@/utils/request";
import { ElMessage, ElMessageBox } from "element-plus";

export default {
  name: "File",

  components: {},

  data() {
    return {
      baseApi: baseURL,
      tableData: [],
      total: 0,
      pageSize: 5,
      currentPage: 1,
      fileName: "",
      multipleSelection: [],
      isUnmounted: false, // ✅ 标记组件是否已卸载
    };
  },

  async created() {
    await this.load();
  },

  beforeUnmount() {
  },

  methods: {
    search() {
      this.currentPage = 1;
      this.load();
    },

    async reload() {
      this.fileName = "";
      this.currentPage = 1;
      await this.load();
    },

    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.load();
    },

    handleCurrentPage(currentPage) {
      this.currentPage = currentPage;
      this.load();
    },

    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleEnable(row){
      this.request.get("/file/enable",{params:{"id": row.id, "enable": row.enable}}).then(res=>{
        if(res.code==='200'){
          this.$message({
            type: "success",
            message: "修改成功",
            duration: 3000
          });
          this.load();
        }else {
          this.$message.error(res.msg);
        }
      })
    },
    async load() {
      if (this.isUnmounted) return; // ✅ 防止组件卸载后更新

      try {
        const res = await apiRequest({
          url: "/file/page",
          method: "get",
          params: {
            pageNum: this.currentPage,
            pageSize: this.pageSize,
            fileName: this.fileName,
          },
        });

        if (this.isUnmounted) return; // ✅ 再次检查

        if (res.code === "200") {
          this.tableData = res.data.records;

          // 格式化文件大小
          for (let s of this.tableData) {
            const size = s.size;
            if (size < 1024) {
              s.size = size + " B";
            } else if (size < 1024 * 1024) {
              s.size = (size / 1024).toFixed(2) + " KB";
            } else if (size < 1024 * 1024 * 1024) {
              s.size = (size / (1024 * 1024)).toFixed(2) + " MB";
            } else {
              s.size = (size / (1024 * 1024 * 1024)).toFixed(2) + " GB";
            }
          }

          this.total = res.data.total;
        } else {
          if (!this.isUnmounted) {
            ElMessage.error(res.msg || "加载失败");
          }
        }
      } catch (e) {
        if (!this.isUnmounted) {
          ElMessage({
            showClose: true,
            message: e.message || "请求失败",
            type: "error",
            duration: 5000,
          });
        }
        console.error(e);
      }
    },

    // 单个删除
    async handleDelete(id) {
      try {
        await ElMessageBox.confirm("确认删除该文件吗？", "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        });
        const res = await apiRequest({
          url: `/file/${id}`,
          method: "delete",
        });
        if (res.code === "200") {
          ElMessage.success("删除成功");
          this.load();
        } else {
          ElMessage.error(res.msg || "删除失败");
        }
      } catch (e) {
        if (e !== "cancel" && !this.isUnmounted) {
          ElMessage.error("删除失败：");
        }
      }
    },

    // 批量删除
    async delBatch() {
      if (this.isUnmounted) return;
      if (!this.multipleSelection.length) {
        ElMessage.warning("请先选择要删除的文件");
        return;
      }

      const ids = this.multipleSelection.map((v) => v.id);

      try {
        await ElMessageBox.confirm("确认删除选中的文件吗？", "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        });

        const res = await apiRequest({
          url: "/file/del/batch",
          method: "post",
          data: ids,
        });

        if (this.isUnmounted) return;

        if (res.code === "200") {
          ElMessage.success("批量删除成功");
          this.load();
        } else {
          ElMessage.error(res.msg || "批量删除失败");
        }
      } catch (e) {
        if (e !== "cancel" && !this.isUnmounted) {
          ElMessage.error("批量删除失败：" + (e.message || "网络错误"));
        }
      }
    },

    // 文件上传成功
    handleFileUploadSuccess(response) {
      if (this.isUnmounted) return;
      if (response.code === "200") {
        ElMessage.success("上传成功");
        this.load();
      } else {
        ElMessage.error(response.msg || "上传失败");
      }
    },
    getDownloadUrl(row) {
      // 如果 url 已是完整链接，直接返回
      if (row.url.startsWith('http')) {
        return row.url;
      }
      // 否则拼接 baseApi，并对路径部分编码
      const fullPath = this.baseApi + row.url;
      return encodeURI(fullPath); // ✅ 使用 encodeURI（适合完整 URL）
    },
  },
  
};
</script>

<style scoped>
 .inputSty {
    width: 250px;padding-right: 5px;
 }
/* 如果需要设置表格背景色，使用 deep */
:deep(.el-table) {
  background-color: transparent;
}
</style>