<!--  -->
<template>
    <div style="text-align: center;width: 100%;font-size: 32px;height: 100%;">
      <h1 style="text-shadow: 1px 1px 1px white, -1px -1px 1px white, 1px -1px 1px white, -1px 1px 1px white;">欢迎访问在线商城后台管理系统</h1>
      <a href="xxx" target='_blank'><font color=red>在线商城</font></a>
    </div>
</template>

<script>
export default {
  name: 'Home', // ✅ 推荐使用多词组件名

  // 引入的组件需要注册
  components: {},

  // 数据区
  data() {
    return {
      
    };
  },

  // 计算属性
  computed: {},

  // 监听数据变化
  watch: {},

  // 方法集合
  methods: {
    
  },

  // 生命周期 - 创建完成
  created() {
    // 
  },

  // 生命周期 - 挂载完成
  mounted() {
    // 
  },

  // 生命周期 - 更新之前
  beforeUpdate() {},

  // 生命周期 - 更新之后
  updated() {},

  // 生命周期 - 卸载前
  beforeUnmount() {
    // 替代 beforeDestroy
    // 
  },

  // 生命周期 - 卸载后
  unmounted() {
    // 替代 destroyed
    // 
  },

  // keep-alive 缓存组件激活时触发
  activated() {},

  // keep-alive 缓存组件失活时触发
  deactivated() {}
}
</script>

<style scoped>

</style>