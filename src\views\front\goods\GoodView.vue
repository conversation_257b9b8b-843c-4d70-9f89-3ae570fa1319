<template>
  <div class="product-detail-container">
    <!-- 主要内容 -->
    <div class="detail-content">
      <!-- 顶部背景装饰 -->
      <div class="bg-decoration">
        <div class="floating-shapes">
          <div class="shape shape-1"></div>
          <div class="shape shape-2"></div>
          <div class="shape shape-3"></div>
        </div>
      </div>

      <!-- 返回按钮 -->
       <el-button @click="goBack">
              <el-icon><ArrowLeft /></el-icon>
           返回
     </el-button>
      <!-- <el-button 
        class="back-btn" 
        type="primary" 
        :icon="ArrowLeft" 
        @click="goBack"
        round
      >
        返回
      </el-button> -->

      <!-- 商品信息卡片 -->
      <div class="product-card" v-motion-slide-visible-bottom>
        <!-- 图片展示区域 -->
        <div class="image-section">
          <div class="main-image-container">
            <el-image
              :src="baseApi +good.imgs"
              fit="cover"
              class="main-image"
              :preview-src-list="baseApi +good.imgs"
              preview-teleported
            >
              <template #placeholder>
                <div class="image-slot">
                  <el-icon><Picture /></el-icon>
                </div>
              </template>
              <template #error>
                <div class="image-slot">
                  <el-icon><Picture /></el-icon>
                  <span>加载失败</span>
                </div>
              </template>
            </el-image>
            
            <!-- 图片指示器 -->
            <div class="image-indicators" v-if="good.imgs.length > 1">
              <div
                v-for="(img, index) in good.imgs"
                :key="index"
                :class="['indicator', { active: mainImageIndex === index }]"
                @click="switchMainImage(index)"
              ></div>
            </div>
          </div>
        </div>

        <!-- 商品信息区域 -->
        <div class="info-section">
          <div class="product-header">
            <h1 class="product-name">{{ good.name }}</h1>
            <div class="status-badge">
              <el-tag type="success" effect="dark" round>热销中</el-tag>
            </div>
          </div>

          <div class="product-description">
            <h3 class="section-title">
              <el-icon><InfoFilled /></el-icon>
              商品描述
            </h3>
            <div class="description-content">
              <p>{{ good.description }}</p>
            </div>
          </div>

          <!-- 操作按钮区域 -->
          <div class="action-buttons">
            <el-button type="primary" @click="goToOrder" size="large" class="action-btn primary-btn">
              <el-icon><ShoppingCart /></el-icon>
              立即购买
            </el-button>
            <el-button type="success" @click="addToCart" size="large" class="action-btn secondary-btn">
              <el-icon><Plus /></el-icon>
              加入购物车
            </el-button>
            <el-button 
              :type="isFavorite ? 'danger' : 'info'" 
              size="large" 
              class="action-btn favorite-btn"
              @click="toggleFavorite"
            >
              <el-icon><Star /></el-icon>
              {{ isFavorite ? '已收藏' : '收藏' }}
            </el-button>
          </div>
         <!-- 分享区域 -->
          <div class="share-section">
               <!--      价格盒子-->
                <div class="price-box" v-if="good.discount < 1">
                    <dl>
                        <div>
                            <dt>原价</dt>
                            <dd style="text-decoration: line-through">
                                <b>{{ price }}</b
                                >元
                            </dd>
                        </div>
                        <div>
                            <dt>折扣</dt>
                            <dd>{{ discount }}</dd>
                        </div>
                        <div>
                            <dt>现价</dt>
                            <dd style="color: red; font-size: 25px">
                                <b>{{ realPrice }}</b
                                >元
                            </dd>
                        </div>
                    </dl>
                </div>
                <div class="price-box" v-if="good.discount === 1">
                    <dl>
                        <div>
                            <dt>价格</dt>
                            <dd style="color: red; font-size: 25px">
                                ￥ <b>{{ price }}</b>
                            </dd>
                        </div>
                    </dl>
                </div>
                <!--      月销量-->
                <div style="margin-top: 20px">
                    <span>月销量：</span>
                    <span>{{ good.sales }}</span
                    ><br />
                    <span style="height: 40px" v-if="showStore"
                        >库存：{{ store }}</span
                    >
                </div>
          </div>
          <!-- 分享区域 -->
          <div class="share-section">
            <span class="share-label">分享给朋友：</span>
            <el-button-group class="share-buttons">
              <el-button size="small">
                <el-icon><Share /></el-icon>
                微信
              </el-button>
              <el-button size="small">
                <el-icon><Share /></el-icon>
                微博
              </el-button>
              <el-button size="small">
                <el-icon><Share /></el-icon>
                QQ
              </el-button>
            </el-button-group>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script >
import { ElMessage } from 'element-plus'
import apiRequest from '@/utils/request' // 假设 apiRequest 是封装好的请求函数
import { baseURL } from '@/utils/request'
// 引入图标（如果模板中需要渲染这些图标，建议注册为组件）
import {
  ArrowLeft,
  Picture,
  InfoFilled,
  ShoppingCart,
  Plus,
  Star,
  Share
} from '@element-plus/icons-vue'

export default {
  name: 'GoodsDetailView', // ✅ 推荐使用多词组件名

  // 注册使用的组件
  components: {
    ArrowLeft,
    Picture,
    InfoFilled,
    ShoppingCart,
    Plus,
    Star,
    Share
  },

  // 数据区
  data() {
    return {
      good: {
        imgs:""
      },
      goodId: Number,
      price: -1,
      isDiscount: false,
      discount: "",
      standards: [],
      checkedStandard: "",
      store: 0,
      showStore: false,
      count: 1,
  
      mainImageIndex: 0,
      isFavorite: false,
       baseApi:baseURL,
    }
  },

  // 计算属性
  computed: {
    mainImage() {
      return this.good.imgs[this.mainImageIndex]
    },
    //折后价，小数点后2位
    realPrice: function () {
        if (this.good.discount < 1) {
            //价格为范围，即不是数字，则返回一个范围
            if (isNaN(this.price)) {
                let down =
                    this.price.substring(0, this.price.indexOf("元")) *
                    this.good.discount;
                let up =
                    this.price.substring(this.price.lastIndexOf(" ")) *
                    this.good.discount;
                return down.toFixed(2) + "元 ~ " + up.toFixed(2);
            } else {
                return (this.price * this.good.discount).toFixed(2);
            }
        }
        return this.price;
    },

  },

  // 监听数据变化
  watch: {},

  // 方法集合
  methods: {
    // 切换主图
    switchMainImage(index) {
      this.mainImageIndex = index
    },

    // 切换收藏状态
    toggleFavorite() {
      this.isFavorite = !this.isFavorite
      ElMessage({
        message: this.isFavorite ? '已添加到收藏' : '已取消收藏',
        type: 'success'
      })
    },

    // 返回上一页
    goBack() {
      this.$router.go(-1)
    },

    // 异步获取商品详情（示例接口调用）
    async getGoodById() {
      try {
        this.goodId = this.$route.params.goodId;
        const res = await apiRequest({ url: '/api/good/'+ this.goodId, method: 'get' })
        if (res.code === '200') {
          console.log("res.data:"+res.data);
          this.good = res.data;
          let discount = this.good.discount;
          if (discount < 1) {
              this.isDiscount = true;
              this.discount = discount * 10 + "折";
          }
        } else {
          this.$router.go(0);
          ElMessage({
            showClose: true,
            message: res.msg || '获取商品信息失败',
            type: 'error',
            duration: 5000
          })
        }
      } catch (e) {
        console.error(e)
        ElMessage({
          showClose: true,
          message: e.message || e || '请求出错',
          type: 'error',
          duration: 5000
        })
      }
    },
    //standard
    async getStandardById() {
      try {
        this.goodId = this.$route.params.goodId;
        const res = await apiRequest({ url: '/api/good/standard/'+ this.goodId, method: 'get' })
        if (res.code === '200') {
                    let standards = JSON.parse(res.data);
                    this.standards = standards;
                    //默认选择第一个标准
                    this.price = this.getPriceRange(standards);
        } else {
          //没有规格
          this.price = this.good.price;
          this.store = this.good.store;
          this.showStore = true;
        }
      } catch (e) {
        console.error(e)
        ElMessage({
          showClose: true,
          message: e.message || e || '请求出错',
          type: 'error',
          duration: 5000
        })
      }
    },
    //购买
    goToOrder() {
           //未登录，拦截
            console.log(localStorage.getItem("user"));
            // if (!localStorage.getItem("user")) {
            //     this.$message.success("您还没有登录，请登录系统");
            //     this.$router.push("/login");
            // }
            if (this.standards.length !== 0) {
                if (this.checkedStandard === "") {
                    this.$message.warning("请选择规格");
                    return false;
                }
            }
            console.log(this.good);
            console.log(this.checkedStandard);
            this.$router.push({
                name: "preOrder",
                query: {
                    good: JSON.stringify(this.good),
                    realPrice: this.realPrice,
                    num: this.count,
                    standard: this.checkedStandard,
                },
            });
        },
     addToCart() {
            //未登录，拦截
            // console.log(localStorage.getItem("user"));
            // if (!localStorage.getItem("user")) {
            //     this.$message.success("您还没有登录，请登录系统");
            //     this.$router.push("/login");
            // }
            if (!this.checkedStandard) {
                this.$message.error("请选择规格");
                return false;
            }
            // 从服务器获取当前用户的id，保证安全
            this.request.get("/userid").then((res) => {
                let userId = res;
                let cart = {
                    userId: userId,
                    goodId: this.goodId,
                    standard: this.checkedStandard,
                    count: this.count,
                };
                this.request.post("/api/cart", cart).then((res) => {
                    if (res.code === "200") {
                        this.$message.success("成功添加购物车");
                    }
                });
            });
        },


  },

  // 生命周期 - 创建完成
  created() {
    // 可在此处调用初始化方法，如获取商品数据
    this.getGoodById()
  },

  // 生命周期 - 挂载完成
  mounted() {
    // 如果需要在 DOM 渲染后操作，可放在这里
  },

  // 生命周期 - 更新之前
  beforeUpdate() {},

  // 生命周期 - 更新之后
  updated() {},

  // 生命周期 - 卸载前
  beforeUnmount() {
    // 替代 beforeDestroy
  },

  // 生命周期 - 卸载后
  unmounted() {
    // 替代 destroyed
  },

  // keep-alive 缓存组件激活时触发
  activated() {
    // 如果使用了 keep-alive，可在这里恢复状态
  },

  // keep-alive 缓存组件失活时触发
  deactivated() {
    // 清理或暂存状态
  }
}
</script>

<style scoped>
.product-detail-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
  padding: 20px;
}

/* 背景装饰 */
.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.floating-shapes {
  position: relative;
  width: 100%;
  height: 100%;
}

.shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 80px;
  height: 80px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 120px;
  height: 120px;
  top: 20%;
  right: 15%;
  animation-delay: 2s;
}

.shape-3 {
  width: 60px;
  height: 60px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

/* 主要内容 */
.detail-content {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
}

/* 返回按钮 */
.back-btn {
  position: fixed;
  top: 20px;
  left: 20px;
  z-index: 100;
  box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
  backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  transition: all 0.3s ease;
}

.back-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(31, 38, 135, 0.5);
}

/* 商品卡片 */
.product-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  padding: 40px;
  margin-top: 80px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 图片区域 */
.image-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.main-image-container {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.main-image {
  width: 100%;
  height: 400px;
  border-radius: 20px;
  transition: transform 0.3s ease;
}

.main-image:hover {
  transform: scale(1.02);
}

.image-slot {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  background: #f5f7fa;
  color: #909399;
  font-size: 14px;
}

.image-slot .el-icon {
  font-size: 40px;
  margin-bottom: 10px;
}

/* 图片指示器 */
.image-indicators {
  position: absolute;
  bottom: 15px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 8px;
}

.indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
}

.indicator.active {
  background: #409eff;
  transform: scale(1.2);
}

/* 缩略图列表 */
.thumbnail-list {
  display: flex;
  gap: 12px;
  overflow-x: auto;
  padding: 10px 0;
}

.thumbnail-item {
  flex-shrink: 0;
  width: 80px;
  height: 80px;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.thumbnail-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.thumbnail-item.active {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.thumbnail-item .el-image {
  width: 100%;
  height: 100%;
}

/* 信息区域 */
.info-section {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.product-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 20px;
}

.product-name {
  margin: 0;
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.2;
}

.status-badge {
  flex-shrink: 0;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1.3rem;
  margin: 0 0 15px 0;
  color: #2c3e50;
}

.description-content {
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa, #c3cfe2);
  border-radius: 16px;
  border-left: 4px solid #409eff;
}

.description-content p {
  margin: 0;
  line-height: 1.8;
  color: #5a6c7d;
  font-size: 1rem;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.action-btn {
  flex: 1;
  min-width: 140px;
  height: 50px;
  border-radius: 25px;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  border: none;
  position: relative;
  overflow: hidden;
}

.primary-btn {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
}

.primary-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 35px rgba(255, 107, 107, 0.6);
}

.secondary-btn {
  background: linear-gradient(135deg, #26de81, #20bf6b);
  box-shadow: 0 8px 25px rgba(38, 222, 129, 0.4);
}

.secondary-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 35px rgba(38, 222, 129, 0.6);
}

.favorite-btn {
  background: linear-gradient(135deg, #fd79a8, #e84393);
  box-shadow: 0 8px 25px rgba(253, 121, 168, 0.4);
}

.favorite-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 35px rgba(253, 121, 168, 0.6);
}

/* 分享区域 */
.share-section {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  background: rgba(64, 158, 255, 0.05);
  border-radius: 16px;
  border: 1px solid rgba(64, 158, 255, 0.1);
}

.share-label {
  font-weight: 500;
  color: #409eff;
}

.share-buttons .el-button {
  border-radius: 20px;
  transition: all 0.3s ease;
}

.share-buttons .el-button:hover {
  transform: translateY(-2px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .product-card {
    grid-template-columns: 1fr;
    gap: 30px;
    padding: 20px;
    margin-top: 60px;
  }
  
  .product-name {
    font-size: 2rem;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .action-btn {
    flex: none;
    width: 100%;
  }
  
  .share-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .back-btn {
    top: 10px;
    left: 10px;
  }
}

/* 动画效果 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.product-card {
  animation: slideInUp 0.6s ease-out;
}
</style>