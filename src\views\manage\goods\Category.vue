<template>
  <div class="category-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>首页</el-breadcrumb-item>
        <el-breadcrumb-item>商品/商品分类管理</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 操作按钮区域 -->
    <div class="operation-bar">
      <el-button type="success" @click="handleAdd" style="font-size: 16px;">
        <el-icon><Plus /></el-icon>
        新增上级分类
      </el-button>
      <div class="stats">
        <span>共{{ total }}条</span>
        <span style="margin-left: 20px;">{{ currentPage }}/{{ Math.ceil(total/pageSize) }}</span>
      </div>
    </div>

    <!-- 分类表格 -->
    <div class="table-container">
      <el-table 
        :data="tableData" 
        stripe 
        style="width: 100%"
        :header-cell-style="{ backgroundColor: '#f0f9ff', color: '#333' }"
      >
        <el-table-column prop="id" label="分类id" width="100" align="center"></el-table-column>
        <el-table-column prop="name" label="分类名称" width="200" align="center"></el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-button 
              type="primary" 
              size="small" 
              @click="handleEdit(scope.row)"
              style="margin-right: 8px;"
            >
              <el-icon><Edit /></el-icon>
              修改
            </el-button>
            <el-button 
              type="success" 
              size="small" 
              @click="handleAddSub(scope.row)"
              style="margin-right: 8px;"
            >
              <el-icon><Plus /></el-icon>
              新增
            </el-button>
            <el-button 
              type="danger" 
              size="small" 
              @click="handleDelete(scope.row)"
            >
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[5, 10, 20, 50]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog 
      v-model="dialogVisible" 
      :title="dialogTitle" 
      width="500px"
      :before-close="handleClose"
    >
      <el-form 
        :model="form" 
        :rules="rules" 
        ref="formRef" 
        label-width="120px"
      >
        <el-form-item label="分类名称" prop="name">
          <el-input 
            v-model="form.name" 
            placeholder="请输入分类名称"
            maxlength="50"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item label="上级分类" v-if="form.parentId">
          <el-input 
            v-model="parentCategoryName" 
            disabled
            placeholder="上级分类"
          ></el-input>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input 
            v-model="form.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入分类描述"
            maxlength="200"
            show-word-limit
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Edit, Delete } from '@element-plus/icons-vue'
import { apiRequest } from '@/utils/request'

export default {
  name: 'CategoryManage',
  components: {
    Plus,
    Edit,
    Delete
  },
  setup() {
    // 响应式数据
    const tableData = ref([])
    const total = ref(0)
    const currentPage = ref(1)
    const pageSize = ref(10)
    const dialogVisible = ref(false)
    const dialogTitle = ref('')
    const parentCategoryName = ref('')
    const formRef = ref()

    // 表单数据
    const form = reactive({
      id: null,
      name: '',
      parentId: null,
      description: ''
    })

    // 表单验证规则
    const rules = {
      name: [
        { required: true, message: '请输入分类名称', trigger: 'blur' },
        { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
      ]
    }

    // 加载分类数据
    const loadCategories = async () => {
      try {
        const res = await apiRequest({
          url: '/api/category/page',
          method: 'get',
          params: {
            pageNum: currentPage.value,
            pageSize: pageSize.value
          }
        })
        if (res.code === '200') {
          tableData.value = res.data.records || []
          total.value = res.data.total || 0
        } else {
          ElMessage.error(res.msg || '获取分类数据失败')
        }
      } catch (error) {
        console.error('加载分类数据失败:', error)
        ElMessage.error('加载分类数据失败')
      }
    }

    // 新增上级分类
    const handleAdd = () => {
      dialogTitle.value = '新增分类'
      Object.assign(form, {
        id: null,
        name: '',
        parentId: null,
        description: ''
      })
      parentCategoryName.value = ''
      dialogVisible.value = true
    }

    // 新增子分类
    const handleAddSub = (row) => {
      dialogTitle.value = '新增子分类'
      Object.assign(form, {
        id: null,
        name: '',
        parentId: row.id,
        description: ''
      })
      parentCategoryName.value = row.name
      dialogVisible.value = true
    }

    // 编辑分类
    const handleEdit = (row) => {
      dialogTitle.value = '编辑分类'
      Object.assign(form, { ...row })
      parentCategoryName.value = row.parentName || ''
      dialogVisible.value = true
    }

    // 删除分类
    const handleDelete = async (row) => {
      try {
        await ElMessageBox.confirm(
          `确定要删除分类"${row.name}"吗？`,
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        
        const res = await apiRequest({
          url: `/api/category/${row.id}`,
          method: 'delete'
        })
        
        if (res.code === '200') {
          ElMessage.success('删除成功')
          loadCategories()
        } else {
          ElMessage.error(res.msg || '删除失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除分类失败:', error)
          ElMessage.error('删除失败')
        }
      }
    }

    // 提交表单
    const handleSubmit = async () => {
      try {
        await formRef.value.validate()
        
        const url = form.id ? '/api/category/update' : '/api/category/save'
        const res = await apiRequest({
          url,
          method: 'post',
          data: form
        })
        
        if (res.code === '200') {
          ElMessage.success(form.id ? '修改成功' : '新增成功')
          dialogVisible.value = false
          loadCategories()
        } else {
          ElMessage.error(res.msg || '操作失败')
        }
      } catch (error) {
        console.error('提交失败:', error)
        ElMessage.error('操作失败')
      }
    }

    // 关闭对话框
    const handleClose = () => {
      dialogVisible.value = false
      formRef.value?.resetFields()
    }

    // 分页相关
    const handleSizeChange = (val) => {
      pageSize.value = val
      currentPage.value = 1
      loadCategories()
    }

    const handleCurrentChange = (val) => {
      currentPage.value = val
      loadCategories()
    }

    // 组件挂载时加载数据
    onMounted(() => {
      loadCategories()
    })

    return {
      tableData,
      total,
      currentPage,
      pageSize,
      dialogVisible,
      dialogTitle,
      parentCategoryName,
      form,
      rules,
      formRef,
      loadCategories,
      handleAdd,
      handleAddSub,
      handleEdit,
      handleDelete,
      handleSubmit,
      handleClose,
      handleSizeChange,
      handleCurrentChange
    }
  }
}
</script>

<style scoped>
.category-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
  padding: 15px 20px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.operation-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px 20px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stats {
  color: #666;
  font-size: 14px;
}

.table-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  overflow: hidden;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  padding: 20px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
