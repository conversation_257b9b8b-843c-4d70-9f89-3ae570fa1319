<template>
  <div class="category-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>首页</el-breadcrumb-item>
        <el-breadcrumb-item>商品/商品分类管理</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 操作按钮区域 -->
    <div class="operation-bar">
      <el-button type="success" @click="handleAdd" style="font-size: 16px;">
        <el-icon><Plus /></el-icon>
        新增上级分类
      </el-button>
      <div class="stats">
        <span>共{{ total }}条</span>
        <span style="margin-left: 20px;">{{ currentPage }}/{{ Math.ceil(total/pageSize) }}</span>
        <span style="margin-left: 20px;">{{ pageSize }}条/页</span>
      </div>
    </div>

    <!-- 分类表格 -->
    <div class="table-container">
      <el-table
        :data="tableData"
        style="width: 100%"
        :header-cell-style="{ backgroundColor: '#e8f4fd', color: '#333', height: '50px' }"
        :row-style="{ height: '60px' }"
      >
        <el-table-column label="分类id" width="120" align="center">
          <template #default="scope">
            <span>{{ scope.row.id }}</span>
          </template>
        </el-table-column>

        <el-table-column label="分类名称" width="300" align="center">
          <template #default="scope">
            <div class="category-name-cell">
              <span v-if="scope.row.level > 1" class="indent-line">
                {{ '  '.repeat((scope.row.level - 1) * 2) }}└─
              </span>
              <i v-if="scope.row.icon"
                 class="category-icon"
                 :style="{ color: getIconColor(scope.row.id) }"
                 v-html="scope.row.icon">
              </i>
              <span class="category-name">{{ scope.row.name }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center">
          <template #default="scope">
            <div class="action-buttons">
              <el-button
                type="primary"
                size="small"
                @click="handleEdit(scope.row)"
                class="action-btn edit-btn"
              >
                <el-icon><Edit /></el-icon>
                修改
              </el-button>
              <el-button
                type="success"
                size="small"
                @click="handleAddSub(scope.row)"
                class="action-btn add-btn"
              >
                <el-icon><Plus /></el-icon>
                新增
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="handleDelete(scope.row)"
                class="action-btn delete-btn"
              >
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[5, 10, 20, 50]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog 
      v-model="dialogVisible" 
      :title="dialogTitle" 
      width="500px"
      :before-close="handleClose"
    >
      <el-form 
        :model="form" 
        :rules="rules" 
        ref="formRef" 
        label-width="120px"
      >
        <el-form-item label="分类名称" prop="name">
          <el-input 
            v-model="form.name" 
            placeholder="请输入分类名称"
            maxlength="50"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item label="上级分类" v-if="form.parentId">
          <el-input 
            v-model="parentCategoryName" 
            disabled
            placeholder="上级分类"
          ></el-input>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input 
            v-model="form.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入分类描述"
            maxlength="200"
            show-word-limit
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Edit, Delete } from '@element-plus/icons-vue'
import { apiRequest } from '@/utils/request'

export default {
  name: 'CategoryManage',
  components: {
    Plus,
    Edit,
    Delete
  },
  setup() {
    // 响应式数据
    const tableData = ref([])
    const total = ref(0)
    const currentPage = ref(1)
    const pageSize = ref(10)
    const dialogVisible = ref(false)
    const dialogTitle = ref('')
    const parentCategoryName = ref('')
    const formRef = ref()

    // 表单数据
    const form = reactive({
      id: null,
      name: '',
      parentId: null,
      description: ''
    })

    // 表单验证规则
    const rules = {
      name: [
        { required: true, message: '请输入分类名称', trigger: 'blur' },
        { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
      ]
    }

    // 获取图标颜色
    const getIconColor = (id) => {
      const colors = ['#409eff', '#67c23a', '#f56c6c', '#e6a23c', '#909399']
      return colors[id % colors.length]
    }

    // 构建层级数据
    const buildTreeData = (data) => {
      const result = []
      const map = {}

      // 创建映射
      data.forEach(item => {
        map[item.id] = { ...item, children: [], level: 1 }
      })

      // 构建树形结构
      data.forEach(item => {
        if (item.parentId && map[item.parentId]) {
          map[item.id].level = map[item.parentId].level + 1
          map[item.parentId].children.push(map[item.id])
        } else {
          result.push(map[item.id])
        }
      })

      // 展平树形结构用于表格显示
      const flattenTree = (nodes, level = 1) => {
        let result = []
        nodes.forEach(node => {
          node.level = level
          result.push(node)
          if (node.children && node.children.length > 0) {
            result = result.concat(flattenTree(node.children, level + 1))
          }
        })
        return result
      }

      return flattenTree(result)
    }

    // 加载分类数据
    const loadCategories = async () => {
      try {
        const res = await apiRequest({
          url: '/api/category/list',
          method: 'get'
        })
        if (res.code === '200') {
          const rawData = res.data || []
          // 模拟一些测试数据
          const mockData = [
            { id: 1, name: '女装', parentId: null, icon: '👗', level: 1 },
            { id: 2, name: '男装', parentId: null, icon: '👔', level: 1 },
            { id: 3, name: '连衣裙', parentId: 1, icon: '👗', level: 2 },
            { id: 4, name: '上衣', parentId: 1, icon: '👚', level: 2 },
            { id: 5, name: '裤装', parentId: 1, icon: '👖', level: 2 },
            { id: 6, name: 'T恤', parentId: 2, icon: '👕', level: 2 },
            { id: 7, name: '衬衫', parentId: 2, icon: '👔', level: 2 },
            { id: 8, name: '休闲裤', parentId: 2, icon: '👖', level: 2 },
            { id: 9, name: '牛仔裤', parentId: 2, icon: '👖', level: 2 },
            { id: 10, name: '短袖T恤', parentId: 6, icon: '👕', level: 3 },
            { id: 11, name: '长袖T恤', parentId: 6, icon: '👕', level: 3 }
          ]

          const processedData = rawData.length > 0 ? buildTreeData(rawData) : mockData
          tableData.value = processedData
          total.value = processedData.length
        } else {
          ElMessage.error(res.msg || '获取分类数据失败')
        }
      } catch (error) {
        console.error('加载分类数据失败:', error)
        // 使用模拟数据
        const mockData = [
          { id: 1, name: '女装', parentId: null, icon: '👗', level: 1 },
          { id: 2, name: '男装', parentId: null, icon: '👔', level: 1 },
          { id: 3, name: '连衣裙', parentId: 1, icon: '👗', level: 2 },
          { id: 4, name: '上衣', parentId: 1, icon: '👚', level: 2 },
          { id: 5, name: '裤装', parentId: 1, icon: '👖', level: 2 },
          { id: 6, name: 'T恤', parentId: 2, icon: '👕', level: 2 },
          { id: 7, name: '衬衫', parentId: 2, icon: '👔', level: 2 },
          { id: 8, name: '休闲裤', parentId: 2, icon: '👖', level: 2 },
          { id: 9, name: '牛仔裤', parentId: 2, icon: '👖', level: 2 },
          { id: 10, name: '短袖T恤', parentId: 6, icon: '👕', level: 3 },
          { id: 11, name: '长袖T恤', parentId: 6, icon: '👕', level: 3 }
        ]
        tableData.value = mockData
        total.value = mockData.length
      }
    }

    // 新增上级分类
    const handleAdd = () => {
      dialogTitle.value = '新增分类'
      Object.assign(form, {
        id: null,
        name: '',
        parentId: null,
        description: ''
      })
      parentCategoryName.value = ''
      dialogVisible.value = true
    }

    // 新增子分类
    const handleAddSub = (row) => {
      dialogTitle.value = '新增子分类'
      Object.assign(form, {
        id: null,
        name: '',
        parentId: row.id,
        description: ''
      })
      parentCategoryName.value = row.name
      dialogVisible.value = true
    }

    // 编辑分类
    const handleEdit = (row) => {
      dialogTitle.value = '编辑分类'
      Object.assign(form, { ...row })
      parentCategoryName.value = row.parentName || ''
      dialogVisible.value = true
    }

    // 删除分类
    const handleDelete = async (row) => {
      try {
        await ElMessageBox.confirm(
          `确定要删除分类"${row.name}"吗？`,
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        
        const res = await apiRequest({
          url: `/api/category/${row.id}`,
          method: 'delete'
        })
        
        if (res.code === '200') {
          ElMessage.success('删除成功')
          loadCategories()
        } else {
          ElMessage.error(res.msg || '删除失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除分类失败:', error)
          ElMessage.error('删除失败')
        }
      }
    }

    // 提交表单
    const handleSubmit = async () => {
      try {
        await formRef.value.validate()
        
        const url = form.id ? '/api/category/update' : '/api/category/save'
        const res = await apiRequest({
          url,
          method: 'post',
          data: form
        })
        
        if (res.code === '200') {
          ElMessage.success(form.id ? '修改成功' : '新增成功')
          dialogVisible.value = false
          loadCategories()
        } else {
          ElMessage.error(res.msg || '操作失败')
        }
      } catch (error) {
        console.error('提交失败:', error)
        ElMessage.error('操作失败')
      }
    }

    // 关闭对话框
    const handleClose = () => {
      dialogVisible.value = false
      formRef.value?.resetFields()
    }

    // 分页相关
    const handleSizeChange = (val) => {
      pageSize.value = val
      currentPage.value = 1
      loadCategories()
    }

    const handleCurrentChange = (val) => {
      currentPage.value = val
      loadCategories()
    }

    // 组件挂载时加载数据
    onMounted(() => {
      loadCategories()
    })

    return {
      tableData,
      total,
      currentPage,
      pageSize,
      dialogVisible,
      dialogTitle,
      parentCategoryName,
      form,
      rules,
      formRef,
      getIconColor,
      buildTreeData,
      loadCategories,
      handleAdd,
      handleAddSub,
      handleEdit,
      handleDelete,
      handleSubmit,
      handleClose,
      handleSizeChange,
      handleCurrentChange
    }
  }
}
</script>

<style scoped>
.category-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
  padding: 15px 20px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.operation-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px 20px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stats {
  color: #666;
  font-size: 14px;
}

.table-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  overflow: hidden;
}

.category-name-cell {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  text-align: left;
}

.indent-line {
  color: #999;
  font-family: monospace;
  margin-right: 5px;
}

.category-icon {
  font-size: 18px;
  margin-right: 8px;
  display: inline-block;
  width: 20px;
  text-align: center;
}

.category-name {
  font-weight: 500;
  color: #333;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.action-btn {
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 12px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.edit-btn {
  background: linear-gradient(135deg, #409eff, #66b3ff);
  color: white;
}

.edit-btn:hover {
  background: linear-gradient(135deg, #337ecc, #5aa3e6);
  transform: translateY(-1px);
}

.add-btn {
  background: linear-gradient(135deg, #67c23a, #85ce61);
  color: white;
}

.add-btn:hover {
  background: linear-gradient(135deg, #529b2e, #6bb344);
  transform: translateY(-1px);
}

.delete-btn {
  background: linear-gradient(135deg, #f56c6c, #f78989);
  color: white;
}

.delete-btn:hover {
  background: linear-gradient(135deg, #f24c4c, #f56c6c);
  transform: translateY(-1px);
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  padding: 20px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 表格行样式 */
:deep(.el-table tbody tr:hover > td) {
  background-color: #f5f7fa !important;
}

:deep(.el-table .cell) {
  padding: 0 10px;
}

/* 表格头部样式 */
:deep(.el-table th) {
  background-color: #e8f4fd !important;
  border-bottom: 1px solid #ddd;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f0f0f0;
}

/* 按钮组样式优化 */
:deep(.el-button + .el-button) {
  margin-left: 8px;
}
</style>
