<!--  -->
<template>
  <div class=""></div>
</template>

<script>
export default {
  name: 'XxxView', // ✅ 推荐使用多词组件名

  // 引入的组件需要注册
  components: {},

  // 数据区
  data() {
    return {
      
    };
  },

  // 计算属性
  computed: {},

  // 监听数据变化
  watch: {},

  // 方法集合
  methods: {
    
  },

  // 生命周期 - 创建完成
  created() {
    // 
  },

  // 生命周期 - 挂载完成
  mounted() {
    // 
  },

  // 生命周期 - 更新之前
  beforeUpdate() {},

  // 生命周期 - 更新之后
  updated() {},

  // 生命周期 - 卸载前
  beforeUnmount() {
    // 替代 beforeDestroy
    // 
  },

  // 生命周期 - 卸载后
  unmounted() {
    // 替代 destroyed
    // 
  },

  // keep-alive 缓存组件激活时触发
  activated() {},

  // keep-alive 缓存组件失活时触发
  deactivated() {}
}
</script>

<style scoped>

</style>